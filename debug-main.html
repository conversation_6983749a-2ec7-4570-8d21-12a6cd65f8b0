<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Main Website</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a1a;
            color: white;
        }
        .debug-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #333;
            border-radius: 5px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background: #28a745; }
        .error { background: #dc3545; }
        .warning { background: #ffc107; color: black; }
        .info { background: #17a2b8; }
        .video-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .video-card {
            border: 1px solid #333;
            border-radius: 8px;
            overflow: hidden;
            background: #2a2a2a;
        }
        .video-card img {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }
        .video-info {
            padding: 10px;
        }
        .video-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <h1>🔍 Debug Main Website</h1>
    <p>This page tests if the main website JavaScript is working correctly.</p>

    <div class="debug-section">
        <h2>JavaScript Loading Status</h2>
        <div id="jsStatus" class="status warning">Checking JavaScript...</div>
    </div>

    <div class="debug-section">
        <h2>App Initialization</h2>
        <div id="appStatus" class="status warning">Checking app initialization...</div>
    </div>

    <div class="debug-section">
        <h2>Data Loading</h2>
        <div id="dataStatus" class="status warning">Checking data loading...</div>
        <div id="videoCount" class="status info" style="display: none;"></div>
        <div id="photoCount" class="status info" style="display: none;"></div>
    </div>

    <div class="debug-section">
        <h2>Sample Videos (First 3)</h2>
        <div id="sampleVideos" class="video-grid">
            <!-- Sample videos will be loaded here -->
        </div>
    </div>

    <script>
        // Debug script to test main.js functionality
        let debugApp = null;

        function updateStatus(elementId, success, message) {
            const element = document.getElementById(elementId);
            element.className = `status ${success ? 'success' : 'error'}`;
            element.textContent = message;
        }

        function showInfo(elementId, message) {
            const element = document.getElementById(elementId);
            element.className = 'status info';
            element.textContent = message;
            element.style.display = 'block';
        }

        // Check if main.js loaded
        setTimeout(() => {
            if (typeof PornTubeX !== 'undefined') {
                updateStatus('jsStatus', true, '✅ main.js loaded successfully');
                
                // Check if app is initialized
                setTimeout(() => {
                    if (window.app && window.app instanceof PornTubeX) {
                        updateStatus('appStatus', true, '✅ App initialized successfully');
                        debugApp = window.app;
                        
                        // Check data loading
                        setTimeout(() => {
                            if (debugApp.videos && debugApp.videos.length > 0) {
                                updateStatus('dataStatus', true, '✅ Data loaded successfully');
                                showInfo('videoCount', `📹 Videos loaded: ${debugApp.videos.length}`);
                                
                                if (debugApp.newPhotos && debugApp.newPhotos.length > 0) {
                                    showInfo('photoCount', `📸 Photos loaded: ${debugApp.newPhotos.length}`);
                                }
                                
                                // Show sample videos
                                loadSampleVideos();
                            } else {
                                updateStatus('dataStatus', false, '❌ No video data found');
                                console.log('Debug - App object:', debugApp);
                                console.log('Debug - Videos array:', debugApp.videos);
                            }
                        }, 1000);
                        
                    } else {
                        updateStatus('appStatus', false, '❌ App not initialized properly');
                        console.log('Debug - window.app:', window.app);
                    }
                }, 500);
                
            } else {
                updateStatus('jsStatus', false, '❌ main.js failed to load or PornTubeX class not found');
                console.log('Debug - Available globals:', Object.keys(window));
            }
        }, 1000);

        function loadSampleVideos() {
            const container = document.getElementById('sampleVideos');
            const sampleVideos = debugApp.videos.slice(0, 3);
            
            sampleVideos.forEach(video => {
                const card = document.createElement('div');
                card.className = 'video-card';
                card.innerHTML = `
                    <img src="${video.thumbnail}" alt="${video.title}" 
                         onerror="this.src='https://via.placeholder.com/300x200?text=Thumbnail+Error'">
                    <div class="video-info">
                        <div class="video-title">${video.title}</div>
                        <div>Duration: ${video.duration}</div>
                        <div>Category: ${video.category}</div>
                        <div>Rating: ${video.rating}/5</div>
                    </div>
                `;
                container.appendChild(card);
            });
        }

        // Log any JavaScript errors
        window.addEventListener('error', (e) => {
            console.error('JavaScript Error:', e.error);
            updateStatus('jsStatus', false, `❌ JavaScript Error: ${e.message}`);
        });
    </script>

    <!-- Load the main scripts -->
    <script src="assets/js/main.js"></script>
</body>
</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final Debug Test</title>
    <link rel="stylesheet" href="assets/css/styles.css">
    <style>
        .debug-panel {
            position: fixed;
            top: 10px;
            right: 10px;
            width: 400px;
            background: #222;
            border: 1px solid #444;
            border-radius: 8px;
            padding: 15px;
            font-size: 12px;
            max-height: 80vh;
            overflow-y: auto;
            z-index: 1000;
        }
        .debug-section {
            margin-bottom: 15px;
            padding: 10px;
            background: #333;
            border-radius: 5px;
        }
        .debug-title {
            font-weight: bold;
            color: #4ecdc4;
            margin-bottom: 5px;
        }
        .success { color: #4caf50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        .info { color: #2196f3; }
    </style>
</head>
<body>
    <!-- Debug Panel -->
    <div class="debug-panel">
        <div class="debug-title">🔍 Debug Panel</div>
        <div id="debugContent">Initializing...</div>
    </div>

    <!-- Main Content (same as index.html) -->
    <header class="header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-brand">
                    <a href="index.html" class="brand-link">
                        <h1 class="brand-title">PornTubeX</h1>
                    </a>
                </div>
            </div>
        </nav>
    </header>

    <main class="main-content">
        <section class="hero-section">
            <div class="hero-content">
                <h2 class="hero-title">Debug Test Page</h2>
                <p class="hero-subtitle">Testing main website functionality</p>
            </div>
        </section>

        <section class="featured-section">
            <div class="container">
                <h3 class="section-title">Featured Videos</h3>
                <div class="videos-grid" id="featuredVideos">
                    <!-- Featured videos will be loaded dynamically -->
                </div>
            </div>
        </section>

        <section class="photos-section">
            <div class="container">
                <h3 class="section-title">Photo Gallery</h3>
                <div class="photos-grid" id="photosGrid">
                    <!-- Photos will be loaded dynamically -->
                </div>
            </div>
        </section>
    </main>

    <script>
        class DebugLogger {
            constructor() {
                this.logs = [];
                this.debugContent = document.getElementById('debugContent');
            }

            log(message, type = 'info') {
                const timestamp = new Date().toLocaleTimeString();
                this.logs.push({ timestamp, message, type });
                this.updateDisplay();
                console.log(`[${timestamp}] ${message}`);
            }

            updateDisplay() {
                const html = this.logs.map(log => 
                    `<div class="${log.type}">[${log.timestamp}] ${log.message}</div>`
                ).join('');
                this.debugContent.innerHTML = html;
                this.debugContent.scrollTop = this.debugContent.scrollHeight;
            }
        }

        const debug = new DebugLogger();

        // Monitor app initialization
        let checkCount = 0;
        function monitorApp() {
            checkCount++;
            
            if (window.app) {
                debug.log('✅ App instance found', 'success');
                
                if (window.app.videos) {
                    debug.log(`📹 Videos array exists: ${window.app.videos.length} items`, 'success');
                    
                    if (window.app.videos.length > 0) {
                        debug.log('🎯 App fully loaded, checking homepage...', 'success');
                        
                        // Check if homepage loaded content
                        setTimeout(() => {
                            const featuredVideos = document.getElementById('featuredVideos');
                            const photosGrid = document.getElementById('photosGrid');
                            
                            debug.log(`Featured videos: ${featuredVideos.children.length} items`, 
                                featuredVideos.children.length > 0 ? 'success' : 'warning');
                            debug.log(`Photos grid: ${photosGrid.children.length} items`, 
                                photosGrid.children.length > 0 ? 'success' : 'warning');
                            
                            if (featuredVideos.children.length === 0 && photosGrid.children.length === 0) {
                                debug.log('⚠️ No content displayed, trying manual load...', 'warning');
                                manualLoad();
                            }
                        }, 2000);
                        
                        return; // Stop monitoring
                    }
                } else {
                    debug.log('❌ Videos array not found', 'error');
                }
            } else {
                debug.log(`⏳ Waiting for app... (attempt ${checkCount})`, 'info');
            }
            
            if (checkCount < 100) {
                setTimeout(monitorApp, 200);
            } else {
                debug.log('❌ Timeout waiting for app', 'error');
            }
        }

        function manualLoad() {
            try {
                debug.log('🔧 Attempting manual content load...', 'info');
                
                const featuredVideos = document.getElementById('featuredVideos');
                const photosGrid = document.getElementById('photosGrid');
                
                // Try to load featured videos manually
                const videos = window.app.videos.filter(v => v.type !== 'photo').slice(0, 6);
                debug.log(`Found ${videos.length} videos for featured section`, 'info');
                
                videos.forEach((video, index) => {
                    try {
                        const card = window.app.createVideoCard(video);
                        featuredVideos.appendChild(card);
                        debug.log(`✅ Added video card ${index + 1}: ${video.title}`, 'success');
                    } catch (error) {
                        debug.log(`❌ Error creating card ${index + 1}: ${error.message}`, 'error');
                    }
                });
                
                // Try to load photos manually
                if (window.app.newPhotos && window.app.newPhotos.length > 0) {
                    const photos = window.app.newPhotos.slice(0, 6);
                    debug.log(`Found ${photos.length} photos for gallery`, 'info');
                    
                    photos.forEach((photo, index) => {
                        try {
                            const card = window.app.createVideoCard(photo);
                            photosGrid.appendChild(card);
                            debug.log(`✅ Added photo card ${index + 1}: ${photo.title}`, 'success');
                        } catch (error) {
                            debug.log(`❌ Error creating photo card ${index + 1}: ${error.message}`, 'error');
                        }
                    });
                }
                
            } catch (error) {
                debug.log(`❌ Manual load failed: ${error.message}`, 'error');
            }
        }

        // Start monitoring
        document.addEventListener('DOMContentLoaded', () => {
            debug.log('🚀 DOM loaded, starting debug monitoring...', 'info');
            setTimeout(monitorApp, 500);
        });

        // Monitor for JavaScript errors
        window.addEventListener('error', (e) => {
            debug.log(`💥 JavaScript Error: ${e.message}`, 'error');
        });
    </script>

    <!-- Load main scripts -->
    <script src="assets/js/main.js"></script>
    <script src="assets/js/homepage.js"></script>
</body>
</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Media Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .media-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .media-item {
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            background: white;
        }
        .media-item img, .media-item video {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }
        .media-info {
            padding: 10px;
        }
        .media-title {
            font-weight: bold;
            margin-bottom: 5px;
            font-size: 14px;
        }
        .media-status {
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 3px;
            color: white;
        }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-loading { background: #ffc107; color: black; }
    </style>
</head>
<body>
    <h1>🧪 Media Test Page</h1>
    <p>This page tests all media files to ensure they load correctly after the fixes.</p>

    <div class="test-section">
        <h2>📹 Video Tests (First 6 Videos)</h2>
        <div class="media-grid" id="videoGrid">
            <!-- Videos will be loaded here -->
        </div>
    </div>

    <div class="test-section">
        <h2>🖼️ Photo Tests (First 6 Photos)</h2>
        <div class="media-grid" id="photoGrid">
            <!-- Photos will be loaded here -->
        </div>
    </div>

    <div class="test-section">
        <h2>🎯 Thumbnail Tests</h2>
        <div class="media-grid" id="thumbnailGrid">
            <!-- Thumbnails will be loaded here -->
        </div>
    </div>

    <script>
        // Test video files
        const testVideos = [
            {
                title: "ANGELA WHITE - Hot Threesome",
                videoUrl: "categories/Videos/ANGELA WHITE - Hot Threesome with Lena Paul and Johnny Sins - Pornhub.com.mp4",
                thumbnail: "categories/Thumbnails/ANGELA WHITE - Hot Threesome with Lena Paul and Johnny Sins - Pornhub.com.jpg"
            },
            {
                title: "2 Lesbians 1 Arab",
                videoUrl: "categories/Videos/2 Lesbians 1 Arab - Pornhub.com.mp4",
                thumbnail: "categories/Thumbnails/2 Lesbians 1 Arab - Pornhub.com.jpg"
            },
            {
                title: "BANGBROS - Mia Khalifa",
                videoUrl: "categories/Videos/BANGBROS - Mia Khalifa Plays with her Gorgoeus Tits until Sean Lawless comes and Fuck her - Pornhub.com.mp4",
                thumbnail: "categories/Thumbnails/BANGBROS - Mia Khalifa Plays with her Gorgoeus Tits until Sean Lawless comes and Fuck her - Pornhub.com.jpg"
            },
            {
                title: "BLACKED Curvy Latina",
                videoUrl: "categories/Videos/BLACKED Curvy Latina Dominated By BBC.mp4",
                thumbnail: "categories/Thumbnails/BLACKED Curvy Latina Dominated By BBC.jpg"
            },
            {
                title: "Big Ass Pakistani Neighbor",
                videoUrl: "categories/Videos/Big Ass Big Tit Pakistani Neighbor Fucks Hard - Pornhub.com.mp4",
                thumbnail: "categories/Thumbnails/Big Ass Big Tit Pakistani Neighbor Fucks Hard - Pornhub.com.jpg"
            },
            {
                title: "Cheated on my Wife",
                videoUrl: "categories/Videos/Cheated on my Wife with my Neighbor. - Pornhub.com.mp4",
                thumbnail: "categories/Thumbnails/Cheated on my Wife with my Neighbor. - Pornhub.com.jpg"
            }
        ];

        // Test photo files
        const testPhotos = [
            {
                title: "Has Sized",
                url: "categories/Photos/006_has-sized.webp"
            },
            {
                title: "Won Her Pussy",
                url: "categories/Photos/009_won-her-pussy.webp"
            },
            {
                title: "Skinny During Sex",
                url: "categories/Photos/011_skinny-during-sex.webp"
            },
            {
                title: "Girl To",
                url: "categories/Photos/012_girl-to.webp"
            },
            {
                title: "Flag",
                url: "categories/Photos/019_flag-.webp"
            },
            {
                title: "Breasty",
                url: "categories/Photos/023_breasty.webp"
            }
        ];

        function createMediaItem(media, type) {
            const item = document.createElement('div');
            item.className = 'media-item';
            
            const mediaElement = type === 'video' ? 
                `<video controls poster="${media.thumbnail}">
                    <source src="${media.videoUrl}" type="video/mp4">
                    Your browser does not support the video tag.
                </video>` :
                `<img src="${media.url}" alt="${media.title}">`;
            
            item.innerHTML = `
                ${mediaElement}
                <div class="media-info">
                    <div class="media-title">${media.title}</div>
                    <div class="media-status status-loading" id="status-${media.title.replace(/\s+/g, '-')}">Loading...</div>
                </div>
            `;
            
            return item;
        }

        function createThumbnailItem(media) {
            const item = document.createElement('div');
            item.className = 'media-item';
            
            item.innerHTML = `
                <img src="${media.thumbnail}" alt="${media.title} Thumbnail">
                <div class="media-info">
                    <div class="media-title">${media.title} (Thumbnail)</div>
                    <div class="media-status status-loading" id="thumb-status-${media.title.replace(/\s+/g, '-')}">Loading...</div>
                </div>
            `;
            
            return item;
        }

        function updateStatus(id, success) {
            const statusEl = document.getElementById(id);
            if (statusEl) {
                statusEl.textContent = success ? 'Loaded ✓' : 'Failed ✗';
                statusEl.className = `media-status ${success ? 'status-success' : 'status-error'}`;
            }
        }

        // Load videos
        const videoGrid = document.getElementById('videoGrid');
        testVideos.forEach(video => {
            const item = createMediaItem(video, 'video');
            videoGrid.appendChild(item);
            
            const videoEl = item.querySelector('video');
            const statusId = `status-${video.title.replace(/\s+/g, '-')}`;
            
            videoEl.addEventListener('loadeddata', () => updateStatus(statusId, true));
            videoEl.addEventListener('error', () => updateStatus(statusId, false));
        });

        // Load photos
        const photoGrid = document.getElementById('photoGrid');
        testPhotos.forEach(photo => {
            const item = createMediaItem(photo, 'photo');
            photoGrid.appendChild(item);
            
            const imgEl = item.querySelector('img');
            const statusId = `status-${photo.title.replace(/\s+/g, '-')}`;
            
            imgEl.addEventListener('load', () => updateStatus(statusId, true));
            imgEl.addEventListener('error', () => updateStatus(statusId, false));
        });

        // Load thumbnails
        const thumbnailGrid = document.getElementById('thumbnailGrid');
        testVideos.forEach(video => {
            const item = createThumbnailItem(video);
            thumbnailGrid.appendChild(item);
            
            const imgEl = item.querySelector('img');
            const statusId = `thumb-status-${video.title.replace(/\s+/g, '-')}`;
            
            imgEl.addEventListener('load', () => updateStatus(statusId, true));
            imgEl.addEventListener('error', () => updateStatus(statusId, false));
        });
    </script>
</body>
</html>

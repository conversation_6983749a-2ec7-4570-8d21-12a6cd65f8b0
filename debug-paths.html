<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Path Debug Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-item {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .loading { background: #fff3cd; border-color: #ffeaa7; }
        img, video { max-width: 200px; max-height: 150px; }
    </style>
</head>
<body>
    <h1>🔍 Path Debug Test</h1>
    <p>Testing direct file access with different path formats:</p>

    <div id="testResults"></div>

    <script>
        const testPaths = [
            // Test different path formats
            {
                name: "Relative Path - Video",
                path: "categories/Videos/ANGELA WHITE - Hot Threesome with <PERSON> and <PERSON> - Pornhub.com.mp4",
                type: "video"
            },
            {
                name: "Relative Path - Thumbnail",
                path: "categories/Thumbnails/ANGELA WHITE - Hot Threesome with Lena Paul and Johnny Sins - Pornhub.com.jpg",
                type: "image"
            },
            {
                name: "Relative Path - Photo",
                path: "categories/Photos/006_has-sized.webp",
                type: "image"
            },
            {
                name: "Absolute Path - Video",
                path: "/categories/Videos/ANGELA WHITE - Hot Threesome with Lena Paul and Johnny Sins - Pornhub.com.mp4",
                type: "video"
            },
            {
                name: "Absolute Path - Thumbnail",
                path: "/categories/Thumbnails/ANGELA WHITE - Hot Threesome with Lena Paul and Johnny Sins - Pornhub.com.jpg",
                type: "image"
            },
            {
                name: "File URL - Video",
                path: "file:///e:/html%20project/categories/Videos/ANGELA%20WHITE%20-%20Hot%20Threesome%20with%20Lena%20Paul%20and%20Johnny%20Sins%20-%20Pornhub.com.mp4",
                type: "video"
            }
        ];

        function createTestElement(test) {
            const div = document.createElement('div');
            div.className = 'test-item loading';
            div.innerHTML = `
                <h3>${test.name}</h3>
                <p><strong>Path:</strong> ${test.path}</p>
                <div id="result-${test.name.replace(/\s+/g, '-')}">Loading...</div>
            `;
            return div;
        }

        function testPath(test, element) {
            const resultDiv = element.querySelector(`#result-${test.name.replace(/\s+/g, '-')}`);
            
            if (test.type === 'image') {
                const img = document.createElement('img');
                img.src = test.path;
                img.onload = () => {
                    element.className = 'test-item success';
                    resultDiv.innerHTML = `✅ Success - Image loaded<br>`;
                    resultDiv.appendChild(img);
                };
                img.onerror = () => {
                    element.className = 'test-item error';
                    resultDiv.innerHTML = `❌ Failed - Image could not load`;
                };
            } else if (test.type === 'video') {
                const video = document.createElement('video');
                video.src = test.path;
                video.controls = true;
                video.onloadeddata = () => {
                    element.className = 'test-item success';
                    resultDiv.innerHTML = `✅ Success - Video loaded<br>`;
                    resultDiv.appendChild(video);
                };
                video.onerror = () => {
                    element.className = 'test-item error';
                    resultDiv.innerHTML = `❌ Failed - Video could not load`;
                };
            }
        }

        // Run tests
        const resultsContainer = document.getElementById('testResults');
        testPaths.forEach(test => {
            const element = createTestElement(test);
            resultsContainer.appendChild(element);
            testPath(test, element);
        });

        // Also test fetch API
        async function testFetchAPI() {
            const fetchDiv = document.createElement('div');
            fetchDiv.className = 'test-item loading';
            fetchDiv.innerHTML = `
                <h3>Fetch API Test</h3>
                <div id="fetch-result">Testing...</div>
            `;
            resultsContainer.appendChild(fetchDiv);

            try {
                const response = await fetch('categories/Videos/ANGELA WHITE - Hot Threesome with Lena Paul and Johnny Sins - Pornhub.com.mp4');
                if (response.ok) {
                    fetchDiv.className = 'test-item success';
                    document.getElementById('fetch-result').innerHTML = `✅ Fetch successful - Status: ${response.status}`;
                } else {
                    fetchDiv.className = 'test-item error';
                    document.getElementById('fetch-result').innerHTML = `❌ Fetch failed - Status: ${response.status}`;
                }
            } catch (error) {
                fetchDiv.className = 'test-item error';
                document.getElementById('fetch-result').innerHTML = `❌ Fetch error: ${error.message}`;
            }
        }

        testFetchAPI();
    </script>
</body>
</html>

@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    FFmpeg Installer for Windows
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo This script requires administrator privileges to modify system PATH.
    echo Please run as administrator.
    echo.
    pause
    exit /b 1
)

echo Running with administrator privileges...
echo.

REM Check if FFmpeg is already installed
echo Checking if FFmpeg is already installed...
ffmpeg -version >nul 2>&1
if %errorlevel% equ 0 (
    echo FFmpeg is already installed and accessible!
    ffmpeg -version | findstr "ffmpeg version"
    echo.
    set /p reinstall="Do you want to reinstall/update FFmpeg? (Y/N): "
    if /i not "!reinstall!"=="Y" if /i not "!reinstall!"=="YES" (
        echo Installation cancelled.
        pause
        exit /b 0
    )
)

echo.
echo Starting FFmpeg installation...
echo ========================================

REM Set installation directory
set "INSTALL_DIR=C:\ffmpeg"
set "BIN_DIR=%INSTALL_DIR%\bin"

REM Create installation directory
echo Creating installation directory: %INSTALL_DIR%
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"
if not exist "%BIN_DIR%" mkdir "%BIN_DIR%"

REM Download FFmpeg (using PowerShell)
echo.
echo Downloading FFmpeg...
echo This may take a few minutes depending on your internet connection...

REM Get the latest FFmpeg build URL (using a stable build)
set "DOWNLOAD_URL=https://www.gyan.dev/ffmpeg/builds/ffmpeg-release-essentials.zip"
set "ZIP_FILE=%TEMP%\ffmpeg.zip"

echo Downloading from: %DOWNLOAD_URL%
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri '%DOWNLOAD_URL%' -OutFile '%ZIP_FILE%' -UseBasicParsing}"

if not exist "%ZIP_FILE%" (
    echo ERROR: Failed to download FFmpeg!
    echo Please check your internet connection and try again.
    pause
    exit /b 1
)

echo Download completed successfully!

REM Extract FFmpeg
echo.
echo Extracting FFmpeg...
powershell -Command "Expand-Archive -Path '%ZIP_FILE%' -DestinationPath '%TEMP%\ffmpeg_extract' -Force"

REM Find the extracted folder (it has a version-specific name)
for /d %%d in ("%TEMP%\ffmpeg_extract\ffmpeg-*") do (
    set "EXTRACTED_DIR=%%d"
)

if not defined EXTRACTED_DIR (
    echo ERROR: Could not find extracted FFmpeg directory!
    pause
    exit /b 1
)

echo Extracted to: !EXTRACTED_DIR!

REM Copy files to installation directory
echo.
echo Installing FFmpeg to %INSTALL_DIR%...
xcopy "!EXTRACTED_DIR!\bin\*" "%BIN_DIR%\" /Y /Q
xcopy "!EXTRACTED_DIR!\doc\*" "%INSTALL_DIR%\doc\" /Y /Q /E /I
xcopy "!EXTRACTED_DIR!\presets\*" "%INSTALL_DIR%\presets\" /Y /Q /E /I

REM Clean up temporary files
echo.
echo Cleaning up temporary files...
del "%ZIP_FILE%" >nul 2>&1
rmdir /s /q "%TEMP%\ffmpeg_extract" >nul 2>&1

REM Add to system PATH
echo.
echo Adding FFmpeg to system PATH...

REM Get current PATH
for /f "tokens=2*" %%a in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH 2^>nul') do set "CURRENT_PATH=%%b"

REM Check if FFmpeg is already in PATH
echo !CURRENT_PATH! | findstr /i "%BIN_DIR%" >nul
if %errorlevel% equ 0 (
    echo FFmpeg directory is already in system PATH.
) else (
    echo Adding %BIN_DIR% to system PATH...
    set "NEW_PATH=!CURRENT_PATH!;%BIN_DIR%"
    reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH /t REG_EXPAND_SZ /d "!NEW_PATH!" /f >nul
    if !errorlevel! equ 0 (
        echo Successfully added to system PATH!
    ) else (
        echo ERROR: Failed to add to system PATH!
        echo You may need to add %BIN_DIR% manually.
    )
)

REM Refresh environment variables for current session
echo.
echo Refreshing environment variables...
set "PATH=%PATH%;%BIN_DIR%"

REM Test installation
echo.
echo Testing FFmpeg installation...
"%BIN_DIR%\ffmpeg.exe" -version >nul 2>&1
if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo    FFmpeg Installation Successful!
    echo ========================================
    echo.
    echo Installation directory: %INSTALL_DIR%
    echo Executable location: %BIN_DIR%\ffmpeg.exe
    echo.
    echo FFmpeg version:
    "%BIN_DIR%\ffmpeg.exe" -version | findstr "ffmpeg version"
    echo.
    echo IMPORTANT: You may need to restart your command prompt
    echo or applications to use the updated PATH variable.
) else (
    echo.
    echo ERROR: FFmpeg installation failed!
    echo The executable was not found or is not working properly.
)

echo.
echo Installation process completed.
pause

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PornTubeX - Premium Adult Content</title>
    <meta name="description" content="PornTubeX - Premium adult content platform with high-quality videos">
    <link rel="stylesheet" href="assets/css/styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-brand">
                    <a href="index.html" class="brand-link">
                        <h1 class="brand-title">PornTubeX</h1>
                    </a>
                </div>
                
                <div class="nav-search">
                    <form class="search-form" id="searchForm">
                        <input type="text" class="search-input" placeholder="Search videos..." id="searchInput">
                        <button type="submit" class="search-btn">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="11" cy="11" r="8"></circle>
                                <path d="m21 21-4.35-4.35"></path>
                            </svg>
                        </button>
                    </form>
                </div>
                
                <div class="nav-menu">
                    <a href="#" class="nav-link">Browse</a>
                    <a href="#" class="nav-link">Categories</a>
                    <a href="#" class="nav-link">Popular</a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Hero Section -->
        <section class="hero-section">
            <div class="hero-content">
                <h2 class="hero-title">Premium Adult Content</h2>
                <p class="hero-subtitle">Discover high-quality, artistic adult content from verified creators</p>
            </div>
        </section>

        <!-- Loading Status -->
        <section class="container">
            <div id="loadingStatus" style="padding: 20px; background: #333; margin: 20px 0; border-radius: 8px; text-align: center;">
                <div style="color: #4ecdc4; font-size: 18px; margin-bottom: 10px;">🔄 Loading Content...</div>
                <div style="color: #b3b3b3; font-size: 14px;">Please wait while we load the latest videos and photos</div>
            </div>
        </section>

        <!-- Featured Videos -->
        <section class="featured-section">
            <div class="container">
                <h3 class="section-title">Featured Videos</h3>
                <div class="videos-grid" id="featuredVideos">
                    <!-- Featured videos will be loaded dynamically -->
                </div>
            </div>
        </section>

        <!-- Photos Section -->
        <section class="photos-section">
            <div class="container">
                <h3 class="section-title">Photo Gallery</h3>
                <div class="photos-grid" id="photosGrid">
                    <!-- Photos will be loaded dynamically -->
                </div>
            </div>
        </section>

        <!-- Recent Videos -->
        <section class="recent-section">
            <div class="container">
                <h3 class="section-title">Recent Videos</h3>
                <div class="videos-grid" id="recentVideos">
                    <!-- Recent videos will be loaded dynamically -->
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>PornTubeX</h4>
                    <p>Premium adult content platform</p>
                </div>
                <div class="footer-section">
                    <h4>Categories</h4>
                    <ul>
                        <li><a href="#">Romantic</a></li>
                        <li><a href="#">Artistic</a></li>
                        <li><a href="#">Wellness</a></li>
                        <li><a href="#">Massage</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Support</h4>
                    <ul>
                        <li><a href="#">Help Center</a></li>
                        <li><a href="#">Contact</a></li>
                        <li><a href="#">Privacy Policy</a></li>
                        <li><a href="#">Terms of Service</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 PornTubeX. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // Simplified content loading that ensures everything works
        function updateLoadingStatus(message, isSuccess = false) {
            const status = document.getElementById('loadingStatus');
            if (isSuccess) {
                status.innerHTML = `
                    <div style="color: #4caf50; font-size: 18px; margin-bottom: 10px;">✅ ${message}</div>
                `;
                setTimeout(() => {
                    status.style.display = 'none';
                }, 3000);
            } else {
                status.innerHTML = `
                    <div style="color: #4ecdc4; font-size: 18px; margin-bottom: 10px;">🔄 ${message}</div>
                `;
            }
        }

        function loadContent() {
            updateLoadingStatus('Initializing application...');
            
            // Wait for app to be ready
            let attempts = 0;
            const maxAttempts = 50;
            
            function checkApp() {
                attempts++;
                
                if (window.app && window.app.videos && window.app.videos.length > 0) {
                    updateLoadingStatus('Content loaded successfully!', true);
                    
                    // Load featured videos
                    const featuredVideos = document.getElementById('featuredVideos');
                    const videos = window.app.videos
                        .filter(video => video.type !== 'photo')
                        .sort((a, b) => b.rating - a.rating)
                        .slice(0, 6);
                    
                    videos.forEach(video => {
                        const card = window.app.createVideoCard(video);
                        featuredVideos.appendChild(card);
                    });
                    
                    // Load photos
                    const photosGrid = document.getElementById('photosGrid');
                    if (window.app.newPhotos && window.app.newPhotos.length > 0) {
                        const photos = window.app.newPhotos
                            .sort((a, b) => b.rating - a.rating)
                            .slice(0, 12);
                        
                        photos.forEach(photo => {
                            const card = window.app.createVideoCard(photo);
                            photosGrid.appendChild(card);
                        });
                    }
                    
                    // Load recent videos
                    const recentVideos = document.getElementById('recentVideos');
                    const recentList = window.app.videos
                        .filter(video => video.type !== 'photo')
                        .slice(6, 14);
                    
                    recentList.forEach(video => {
                        const card = window.app.createVideoCard(video);
                        recentVideos.appendChild(card);
                    });
                    
                } else if (attempts < maxAttempts) {
                    updateLoadingStatus(`Loading data... (${attempts}/${maxAttempts})`);
                    setTimeout(checkApp, 200);
                } else {
                    updateLoadingStatus('Failed to load content. Please refresh the page.', false);
                }
            }
            
            checkApp();
        }

        // Start loading when DOM is ready
        document.addEventListener('DOMContentLoaded', loadContent);
    </script>

    <!-- Scripts -->
    <script src="assets/js/main.js"></script>
</body>
</html>

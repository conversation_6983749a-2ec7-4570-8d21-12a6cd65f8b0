<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Card Test</title>
    <link rel="stylesheet" href="assets/css/styles.css">
</head>
<body>
    <div class="container">
        <h1>Video Card Test</h1>
        
        <div id="status" style="padding: 20px; background: #333; margin: 20px 0; border-radius: 5px;">
            Testing video card creation...
        </div>
        
        <div class="videos-grid" id="testGrid">
            <!-- Test cards will be added here -->
        </div>
    </div>

    <script>
        function updateStatus(message) {
            document.getElementById('status').innerHTML = message;
            console.log(message);
        }

        function testVideoCard() {
            // Wait for app to be available
            if (!window.app) {
                setTimeout(testVideoCard, 100);
                return;
            }

            if (!window.app.videos || window.app.videos.length === 0) {
                setTimeout(testVideoCard, 100);
                return;
            }

            try {
                updateStatus('✅ App loaded, testing video card creation...');
                
                const testGrid = document.getElementById('testGrid');
                const sampleVideo = window.app.videos[0];
                
                updateStatus(`Testing with video: ${sampleVideo.title}`);
                
                // Test the createVideoCard method
                const videoCard = window.app.createVideoCard(sampleVideo);
                testGrid.appendChild(videoCard);
                
                updateStatus(`✅ Successfully created and added video card!`);
                
                // Add a few more for good measure
                for (let i = 1; i < Math.min(6, window.app.videos.length); i++) {
                    const card = window.app.createVideoCard(window.app.videos[i]);
                    testGrid.appendChild(card);
                }
                
                updateStatus(`✅ Added ${testGrid.children.length} video cards total`);
                
            } catch (error) {
                updateStatus(`❌ Error creating video card: ${error.message}`);
                console.error('Error:', error);
            }
        }

        // Start the test
        document.addEventListener('DOMContentLoaded', () => {
            testVideoCard();
        });
    </script>

    <script src="assets/js/main.js"></script>
</body>
</html>

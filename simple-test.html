<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a1a;
            color: white;
        }
        .video-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .video-card {
            border: 1px solid #333;
            border-radius: 8px;
            overflow: hidden;
            background: #2a2a2a;
            padding: 10px;
        }
        .video-card img {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 5px;
        }
        .video-title {
            font-weight: bold;
            margin: 10px 0 5px 0;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            background: #333;
        }
    </style>
</head>
<body>
    <h1>🧪 Simple Test - Direct Data Access</h1>
    
    <div id="status" class="status">Loading...</div>
    
    <div id="videoContainer" class="video-grid">
        <!-- Videos will be loaded here -->
    </div>

    <script>
        // Test the embedded data directly without the full app
        function testEmbeddedData() {
            try {
                // Create a temporary instance to access the embedded data
                const tempApp = {
                    getEmbeddedVideoData() {
                        // Copy the exact data from main.js
                        return [
                            {
                                id: "v_1",
                                title: "ANGELA WHITE - Hot Threesome with Lena Paul and Johnny Sins",
                                description: "ANGELA WHITE - Hot Threesome with Lena Paul and Johnny Sins",
                                thumbnail: "categories/Thumbnails/ANGELA WHITE - Hot Threesome with Lena Paul and Johnny Sins - Pornhub.com.jpg",
                                videoUrl: "categories/Videos/ANGELA WHITE - Hot Threesome with Lena Paul and Johnny Sins - Pornhub.com.mp4",
                                duration: "25:30",
                                uploadDate: "2025-07-02",
                                uploader: { id: "u_new_1", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/1.jpg" },
                                tags: ["threesome", "adult", "angela white"],
                                category: "new_videos",
                                subcategory: "threesome",
                                views: 45230,
                                likes: 1250,
                                rating: 4.7,
                                type: "video"
                            },
                            {
                                id: "v_2",
                                title: "2 Lesbians 1 Arab",
                                description: "2 Lesbians 1 Arab",
                                thumbnail: "categories/Thumbnails/2 Lesbians 1 Arab - Pornhub.com.jpg",
                                videoUrl: "categories/Videos/2 Lesbians 1 Arab - Pornhub.com.mp4",
                                duration: "34:45",
                                uploadDate: "2025-07-02",
                                uploader: { id: "u_new_2", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/2.jpg" },
                                tags: ["lesbian", "adult"],
                                category: "new_videos",
                                subcategory: "lesbian",
                                views: 32150,
                                likes: 890,
                                rating: 4.5,
                                type: "video"
                            },
                            {
                                id: "v_3",
                                title: "BANGBROS - Mia Khalifa Plays with her Gorgeous Tits",
                                description: "BANGBROS - Mia Khalifa Plays with her Gorgeous Tits",
                                thumbnail: "categories/Thumbnails/BANGBROS - Mia Khalifa Plays with her Gorgeous Tits in a Sensual Solo Scene - Pornhub.com.jpg",
                                videoUrl: "categories/Videos/BANGBROS - Mia Khalifa Plays with her Gorgeous Tits in a Sensual Solo Scene - Pornhub.com.mp4",
                                duration: "16:00",
                                uploadDate: "2025-07-02",
                                uploader: { id: "u_new_3", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/3.jpg" },
                                tags: ["solo", "adult", "mia khalifa"],
                                category: "new_videos",
                                subcategory: "solo",
                                views: 67890,
                                likes: 2340,
                                rating: 4.9,
                                type: "video"
                            }
                        ];
                    }
                };

                const videos = tempApp.getEmbeddedVideoData();
                
                document.getElementById('status').innerHTML = `
                    ✅ Successfully loaded ${videos.length} videos directly from embedded data
                `;

                const container = document.getElementById('videoContainer');
                
                videos.forEach(video => {
                    const card = document.createElement('div');
                    card.className = 'video-card';
                    card.innerHTML = `
                        <img src="${video.thumbnail}" alt="${video.title}" 
                             onerror="this.src='https://via.placeholder.com/300x200?text=Thumbnail+Error'">
                        <div class="video-title">${video.title}</div>
                        <div>Duration: ${video.duration}</div>
                        <div>Views: ${video.views.toLocaleString()}</div>
                        <div>Rating: ${video.rating}/5</div>
                    `;
                    container.appendChild(card);
                });

            } catch (error) {
                document.getElementById('status').innerHTML = `
                    ❌ Error: ${error.message}
                `;
                console.error('Error:', error);
            }
        }

        // Run the test
        testEmbeddedData();
    </script>
</body>
</html>
